# VSCode Extension Packager
# 功能：交互式选择文件夹并打包为VSCode插件(.vsix)格式
# 作者：Augment Agent
# 日期：2025-01-08

param(
    [string]$TargetPath = "."
)

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "VSCode Extension Packager 📦"

# 颜色和图标定义
$Colors = @{
    Header = "Cyan"
    Success = "Green" 
    Warning = "Yellow"
    Error = "Red"
    Info = "White"
    Highlight = "Magenta"
}

$Icons = @{
    Folder = "📁"
    Package = "📦"
    Success = "✅"
    Error = "❌"
    Warning = "⚠️"
    Info = "ℹ️"
    Arrow = "➤"
    Time = "🕒"
}

# 清屏并显示标题
function Show-Header {
    Clear-Host
    Write-Host "`n" -NoNewline
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor $Colors.Header
    Write-Host "║" -ForegroundColor $Colors.Header -NoNewline
    Write-Host "          VSCode Extension Packager $($Icons.Package)                    " -ForegroundColor $Colors.Header -NoNewline
    Write-Host "║" -ForegroundColor $Colors.Header
    Write-Host "║" -ForegroundColor $Colors.Header -NoNewline
    Write-Host "     将文件夹打包为VSCode插件(.vsix)格式                      " -ForegroundColor $Colors.Info -NoNewline
    Write-Host "║" -ForegroundColor $Colors.Header
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor $Colors.Header
    Write-Host ""
}

# 获取文件夹列表并按修改时间排序（排除隐藏文件夹）
function Get-FolderList {
    param([string]$Path)

    try {
        $folders = Get-ChildItem -Path $Path -Directory |
                   Where-Object { -not $_.Name.StartsWith('.') } |  # 排除以点号开头的隐藏文件夹
                   Sort-Object LastWriteTime -Descending |
                   Select-Object Name, LastWriteTime

        if ($folders.Count -eq 0) {
            Write-Host "$($Icons.Warning) 当前目录下没有找到任何可用文件夹（已排除隐藏文件夹）" -ForegroundColor $Colors.Warning
            return $null
        }

        Write-Host "$($Icons.Info) 已排除隐藏文件夹（以 . 开头的文件夹）" -ForegroundColor "DarkGray"
        return $folders
    }
    catch {
        Write-Host "$($Icons.Error) 读取目录失败: $($_.Exception.Message)" -ForegroundColor $Colors.Error
        return $null
    }
}

# 显示交互式菜单
function Show-InteractiveMenu {
    param(
        [array]$Folders,
        [int]$SelectedIndex = 0
    )
    
    Show-Header
    Write-Host "$($Icons.Info) 请使用 ↑↓ 箭头键选择要打包的文件夹，按 Enter 确认，按 Esc 退出`n" -ForegroundColor $Colors.Info
    
    for ($i = 0; $i -lt $Folders.Count; $i++) {
        $folder = $Folders[$i]
        $timeStr = $folder.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
        
        if ($i -eq $SelectedIndex) {
            # 高亮显示选中项
            Write-Host "$($Icons.Arrow) " -ForegroundColor $Colors.Highlight -NoNewline
            Write-Host "$($Icons.Folder) $($folder.Name)" -ForegroundColor $Colors.Highlight -NoNewline
            Write-Host "    $($Icons.Time) $timeStr" -ForegroundColor $Colors.Highlight
        } else {
            Write-Host "   $($Icons.Folder) $($folder.Name)" -ForegroundColor $Colors.Info -NoNewline
            Write-Host "    $($Icons.Time) $timeStr" -ForegroundColor "DarkGray"
        }
    }
    
    Write-Host "`n" -NoNewline
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor "DarkGray"
    Write-Host "总共找到 $($Folders.Count) 个文件夹" -ForegroundColor "DarkGray"
}

# 处理键盘输入
function Get-UserSelection {
    param([array]$Folders)
    
    $selectedIndex = 0
    
    do {
        Show-InteractiveMenu -Folders $Folders -SelectedIndex $selectedIndex
        
        $key = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
        switch ($key.VirtualKeyCode) {
            38 { # 上箭头
                $selectedIndex = [Math]::Max(0, $selectedIndex - 1)
            }
            40 { # 下箭头  
                $selectedIndex = [Math]::Min($Folders.Count - 1, $selectedIndex + 1)
            }
            13 { # Enter
                return $Folders[$selectedIndex]
            }
            27 { # Esc
                Write-Host "`n$($Icons.Info) 用户取消操作" -ForegroundColor $Colors.Info
                return $null
            }
        }
    } while ($true)
}

# 智能查找package.json文件
function Find-PackageJsonPath {
    param([string]$FolderPath)

    # 常见的package.json可能存在的路径
    $searchPaths = @(
        "package.json",                    # 根目录
        "extension\package.json",          # extension子目录
        "src\package.json",               # src子目录
        "app\package.json",               # app子目录
        "client\package.json"             # client子目录
    )

    foreach ($relativePath in $searchPaths) {
        $fullPath = Join-Path $FolderPath $relativePath
        if (Test-Path $fullPath) {
            Write-Host "$($Icons.Info) 找到 package.json: $relativePath" -ForegroundColor "DarkGray"
            return $fullPath
        }
    }

    return $null
}

# 修改package.json中的displayName字段
function Update-PackageJson {
    param(
        [string]$FolderPath,
        [string]$NewDisplayName = "Aug-魔改去风控(💩)版"
    )

    try {
        # 智能查找package.json文件
        $packageJsonPath = Find-PackageJsonPath -FolderPath $FolderPath

        if (-not $packageJsonPath) {
            Write-Host "$($Icons.Warning) 未找到 package.json 文件（已搜索常见位置）" -ForegroundColor $Colors.Warning
            return $false
        }

        Write-Host "$($Icons.Info) 正在修改 package.json..." -ForegroundColor $Colors.Info

        # 读取JSON文件
        $jsonContent = Get-Content $packageJsonPath -Raw -Encoding UTF8
        $packageObj = $jsonContent | ConvertFrom-Json

        # 检查是否存在displayName字段
        if (-not $packageObj.PSObject.Properties.Name -contains "displayName") {
            Write-Host "$($Icons.Warning) package.json 中未找到 displayName 字段" -ForegroundColor $Colors.Warning
            return $false
        }

        $oldDisplayName = $packageObj.displayName
        $packageObj.displayName = $NewDisplayName

        # 保存修改后的JSON
        $packageObj | ConvertTo-Json -Depth 100 | Set-Content $packageJsonPath -Encoding UTF8

        Write-Host "$($Icons.Success) displayName 已更新: '$oldDisplayName' → '$NewDisplayName'" -ForegroundColor $Colors.Success
        Write-Host "$($Icons.Info) 文件位置: $packageJsonPath" -ForegroundColor "DarkGray"
        return $true
    }
    catch {
        Write-Host "$($Icons.Error) 修改 package.json 失败: $($_.Exception.Message)" -ForegroundColor $Colors.Error
        return $false
    }
}

# 智能查找图标文件
function Find-IconFiles {
    param([string]$FolderPath)

    # 常见的图标文件名
    $iconFileNames = @("icon.png", "logo.png", "icon.jpg", "icon.jpeg", "logo.jpg", "logo.jpeg", "icon.svg", "logo.svg")

    # 常见的图标文件可能存在的目录
    $searchDirs = @(
        "",                    # 根目录
        "extension",           # extension子目录
        "src",                # src子目录
        "app",                # app子目录
        "client",             # client子目录
        "assets",             # assets子目录
        "images",             # images子目录
        "media"               # media子目录
    )

    $foundIcons = @()

    foreach ($dir in $searchDirs) {
        $searchPath = if ($dir -eq "") { $FolderPath } else { Join-Path $FolderPath $dir }

        if (Test-Path $searchPath) {
            foreach ($iconName in $iconFileNames) {
                $iconPath = Join-Path $searchPath $iconName
                if (Test-Path $iconPath) {
                    $relativePath = if ($dir -eq "") { $iconName } else { Join-Path $dir $iconName }
                    $foundIcons += @{
                        FullPath = $iconPath
                        RelativePath = $relativePath
                        FileName = $iconName
                    }
                }
            }
        }
    }

    return $foundIcons
}

# 替换图标文件
function Update-IconFile {
    param(
        [string]$FolderPath,
        [string]$SourceLogoPath
    )

    try {
        if (-not (Test-Path $SourceLogoPath)) {
            Write-Host "$($Icons.Warning) 源logo文件不存在: $SourceLogoPath" -ForegroundColor $Colors.Warning
            return $false
        }

        Write-Host "$($Icons.Info) 正在查找并替换图标文件..." -ForegroundColor $Colors.Info

        # 智能查找图标文件
        $foundIcons = Find-IconFiles -FolderPath $FolderPath

        if ($foundIcons.Count -eq 0) {
            Write-Host "$($Icons.Warning) 未找到常见的图标文件（已搜索多个目录）" -ForegroundColor $Colors.Warning
            return $false
        }

        $replacedCount = 0
        foreach ($icon in $foundIcons) {
            try {
                # 备份原图标
                $backupPath = $icon.FullPath + ".backup"
                Copy-Item $icon.FullPath $backupPath -Force

                # 替换图标
                Copy-Item $SourceLogoPath $icon.FullPath -Force

                Write-Host "$($Icons.Success) 已替换图标文件: $($icon.RelativePath)" -ForegroundColor $Colors.Success
                Write-Host "$($Icons.Info) 原文件已备份为: $($icon.RelativePath).backup" -ForegroundColor "DarkGray"
                $replacedCount++
            }
            catch {
                Write-Host "$($Icons.Warning) 替换 $($icon.RelativePath) 失败: $($_.Exception.Message)" -ForegroundColor $Colors.Warning
            }
        }

        if ($replacedCount -gt 0) {
            Write-Host "$($Icons.Success) 总共替换了 $replacedCount 个图标文件" -ForegroundColor $Colors.Success
            return $true
        } else {
            Write-Host "$($Icons.Error) 没有成功替换任何图标文件" -ForegroundColor $Colors.Error
            return $false
        }
    }
    catch {
        Write-Host "$($Icons.Error) 替换图标文件失败: $($_.Exception.Message)" -ForegroundColor $Colors.Error
        return $false
    }
}

# 创建VSIX包
function New-VSIXPackage {
    param(
        [string]$SourceFolder,
        [string]$OutputPath,
        [string]$WorkspaceRoot
    )

    try {
        Write-Host "`n$($Icons.Package) 开始打包文件夹: $SourceFolder" -ForegroundColor $Colors.Info

        # 检查源文件夹是否存在
        if (-not (Test-Path $SourceFolder)) {
            throw "源文件夹不存在: $SourceFolder"
        }

        # 执行预处理步骤
        Write-Host "`n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor "DarkGray"
        Write-Host "🔧 执行打包前预处理..." -ForegroundColor $Colors.Info
        Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor "DarkGray"

        # 1. 修改package.json
        $packageResult = Update-PackageJson -FolderPath $SourceFolder

        # 2. 替换图标文件
        $logoPath = Join-Path $WorkspaceRoot "logo.png"
        $iconResult = Update-IconFile -FolderPath $SourceFolder -SourceLogoPath $logoPath

        Write-Host "`n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor "DarkGray"
        Write-Host "📦 开始ZIP压缩..." -ForegroundColor $Colors.Info
        Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor "DarkGray"

        # 创建临时ZIP文件
        $tempZip = [System.IO.Path]::GetTempFileName() + ".zip"

        Write-Host "$($Icons.Info) 正在压缩文件..." -ForegroundColor $Colors.Info

        # 使用.NET压缩功能
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::CreateFromDirectory($SourceFolder, $tempZip)

        # 移动并重命名为.vsix
        Move-Item $tempZip $OutputPath -Force

        Write-Host "$($Icons.Success) 打包完成!" -ForegroundColor $Colors.Success
        Write-Host "$($Icons.Info) 输出文件: $OutputPath" -ForegroundColor $Colors.Info

        # 显示文件信息
        $fileInfo = Get-Item $OutputPath
        $sizeKB = [Math]::Round($fileInfo.Length / 1KB, 2)
        Write-Host "$($Icons.Info) 文件大小: $sizeKB KB" -ForegroundColor $Colors.Info
        Write-Host "$($Icons.Time) 创建时间: $($fileInfo.CreationTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor $Colors.Info
        # 显示文件路径
        Write-Host "$($Icons.Info) 文件路径: $OutputPath" -ForegroundColor $Colors.Info

        # 显示预处理结果摘要
        Write-Host "`n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor "DarkGray"
        Write-Host "📋 预处理结果摘要:" -ForegroundColor $Colors.Info
        Write-Host "   • package.json 修改: $(if($packageResult){'✅ 成功'}else{'❌ 失败/跳过'})" -ForegroundColor $(if($packageResult){$Colors.Success}else{$Colors.Warning})
        Write-Host "   • 图标文件替换: $(if($iconResult){'✅ 成功'}else{'❌ 失败/跳过'})" -ForegroundColor $(if($iconResult){$Colors.Success}else{$Colors.Warning})
        Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor "DarkGray"

        return $true
    }
    catch {
        Write-Host "$($Icons.Error) 打包失败: $($_.Exception.Message)" -ForegroundColor $Colors.Error
        return $false
    }
}

# 主函数
function Main {
    Show-Header
    
    Write-Host "$($Icons.Info) 扫描目录: $(Resolve-Path $TargetPath)" -ForegroundColor $Colors.Info
    Write-Host "$($Icons.Info) 正在获取文件夹列表..." -ForegroundColor $Colors.Info
    
    # 获取文件夹列表
    $folders = Get-FolderList -Path $TargetPath
    if (-not $folders) {
        Write-Host "`n按任意键退出..." -ForegroundColor $Colors.Info
        $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") | Out-Null
        return
    }
    
    # 用户选择
    $selectedFolder = Get-UserSelection -Folders $folders
    if (-not $selectedFolder) {
        return
    }
    
    # 确认选择
    Show-Header
    Write-Host "$($Icons.Arrow) 您选择了文件夹: " -ForegroundColor $Colors.Info -NoNewline
    Write-Host "$($selectedFolder.Name)" -ForegroundColor $Colors.Highlight
    Write-Host "$($Icons.Time) 修改时间: $($selectedFolder.LastWriteTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor $Colors.Info
    
    Write-Host "`n确认打包此文件夹吗? [Y/n] (默认: Y): " -ForegroundColor $Colors.Warning -NoNewline
    $confirm = Read-Host

    # 空输入或Y/y都视为确认，只有明确输入N/n才取消
    if ($confirm -match '^[Nn]') {
        Write-Host "$($Icons.Info) 操作已取消" -ForegroundColor $Colors.Info
        return
    }
    
    # 生成输出文件名
    $outputFileName = "vscode-augment-$($selectedFolder.Name).vsix"
    $outputPath = Join-Path $TargetPath $outputFileName
    $sourcePath = Join-Path $TargetPath $selectedFolder.Name
    
    # 执行打包（传入工作区根路径用于logo文件）
    $workspaceRoot = $TargetPath
    $success = New-VSIXPackage -SourceFolder $sourcePath -OutputPath $outputPath -WorkspaceRoot $workspaceRoot
    
    if ($success) {
        Write-Host "`n$($Icons.Success) 任务完成! VSCode插件已成功打包" -ForegroundColor $Colors.Success
        Write-Host "$($Icons.Package) 可以通过VSCode安装此插件文件" -ForegroundColor $Colors.Info
        # 打包成功时间
        Write-Host "$($Icons.Time) 打包成功时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor $Colors.Info
        # 打包成功后，打开文件夹
        # Start-Process $outputPath
    }
    
    Write-Host "`n按任意键退出..." -ForegroundColor $Colors.Info
    $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") | Out-Null
}

# 脚本入口点
try {
    Main
}
catch {
    Write-Host "`n$($Icons.Error) 脚本执行出错: $($_.Exception.Message)" -ForegroundColor $Colors.Error
    Write-Host "按任意键退出..." -ForegroundColor $Colors.Info
    $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") | Out-Null
}
